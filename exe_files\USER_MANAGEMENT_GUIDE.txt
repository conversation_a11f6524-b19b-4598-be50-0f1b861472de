👥 دليل إدارة المستخدمين - للمطور
=====================================

📋 نظام تسجيل الدخول الجديد:
============================

🔐 كيف يعمل النظام:
1. المستخدم يدخل الإيميل وكلمة المرور
2. البرنامج يتحقق من قاعدة البيانات المشفرة
3. إذا كان المستخدم موجود ولديه ترخيص صالح - يدخل مباشرة
4. إذا كان المستخدم موجود بدون ترخيص - يطلب التفعيل
5. إذا كان مستخدم جديد - يطلب التسجيل والتفعيل

📊 قاعدة البيانات:
==================
- الملف: users.enc (مشفر)
- يحتوي على: الإيميل، كلمة المرور، الاسم، تاريخ الإنشاء
- مشفر بـ base64 لحماية البيانات

👥 المستخدمين التجريبيين:
==========================
1. <EMAIL> / admin123
2. <EMAIL> / user123  
3. <EMAIL> / demo123

🔧 إضافة مستخدمين جدد:
======================
1. اذهب إلى مجلد EndUser
2. عدل ملف create_users_db.py
3. أضف المستخدم الجديد في قائمة users
4. شغل الملف: python create_users_db.py
5. سيتم تحديث قاعدة البيانات

مثال إضافة مستخدم:
{
    "email": "<EMAIL>",
    "password": "newpass123",
    "name": "New User",
    "created_date": "2024-12-01"
}

🎯 سيناريوهات الاستخدام:
========================

📝 مستخدم جديد تماماً:
- يدخل إيميل وباسورد جديد
- يظهر له "حساب جديد"
- يطلب منه التسجيل والتفعيل
- يرسل البيانات للمطور
- المطور ينشئ له حساب وترخيص

👤 مستخدم موجود بدون ترخيص:
- يدخل إيميل وباسورد صحيح
- يظهر له "تفعيل مطلوب"
- يطلب منه تفعيل الجهاز الجديد
- يرسل البيانات للمطور
- المطور ينشئ له ترخيص للجهاز الجديد

✅ مستخدم موجود مع ترخيص صالح:
- يدخل إيميل وباسورد صحيح
- يدخل للبرنامج مباشرة
- لا يحتاج تفعيل إضافي

🔒 الأمان:
==========
- قاعدة البيانات مشفرة
- كلمات المرور مخزنة كما هي (يمكن تحسينها بـ hash)
- ربط التراخيص بمعرف الجهاز
- تحقق محلي بدون إنترنت

💡 نصائح للمطور:
=================
1. احتفظ بنسخة احتياطية من users.enc
2. يمكنك تغيير كلمات المرور التجريبية
3. أضف مستخدمين حقيقيين حسب الحاجة
4. راقب طلبات التفعيل الجديدة
5. أنشئ تراخيص للمستخدمين المدفوعين فقط

🚀 التطوير المستقبلي:
=====================
- إضافة تشفير أقوى لكلمات المرور
- نظام انتهاء صلاحية للتراخيص
- إحصائيات استخدام المستخدمين
- نظام تجديد التراخيص التلقائي

---
📧 للدعم التقني: استخدم روابط التواصل في البرنامج
🔧 تم التطوير بواسطة: Augment Agent
