#!/usr/bin/env python3
"""
Local Software Activation System - End User Application
Main application with local license verification
"""

import tkinter as tk
from tkinter import messagebox
from tkinter import ttk
import hashlib
import json
import os
import base64
import uuid
import platform
import subprocess
import webbrowser

# Configuration
LICENSE_FILE = "license.key"
SECRET_KEY = "SecureKey2024!@#$%"  # Must match license generator

def get_hardware_id():
    """Generate unique hardware ID based on system information"""
    try:
        # Get system information
        system_info = platform.system() + platform.version() + platform.machine()
        
        # Get additional hardware info on Windows
        if platform.system() == "Windows":
            try:
                result = subprocess.check_output('wmic csproduct get uuid', shell=True).decode()
                uuid_result = result.split('\n')[1].strip()
                system_info += uuid_result
            except:
                pass
        
        # Add MAC address
        system_info += str(uuid.getnode())
        
        # Create hash and format
        hardware_hash = hashlib.sha256(system_info.encode()).hexdigest()
        formatted_id = f"{hardware_hash[:4]}-{hardware_hash[4:8]}-{hardware_hash[8:12]}-{hardware_hash[12:16]}"
        return formatted_id.upper()
    except:
        # Fallback to simple MAC address
        node = str(uuid.getnode())
        return f"{node[:4]}-{node[4:8]}-{node[8:12]}-{node[12:16]}".upper()

def create_signature(email, password, hwid):
    """Create SHA256 signature for verification"""
    data = f"{email}{password}{hwid}{SECRET_KEY}"
    return hashlib.sha256(data.encode()).hexdigest()

def verify_license():
    """Verify if valid license exists"""
    if not os.path.exists(LICENSE_FILE):
        return False, "License file not found"
    
    try:
        # Read and decode license file
        with open(LICENSE_FILE, "r") as f:
            encoded_data = f.read().strip()
        
        decoded_data = base64.b64decode(encoded_data).decode()
        license_data = json.loads(decoded_data)
        
        # Extract license information
        stored_email = license_data.get("email", "")
        stored_hwid = license_data.get("hwid", "")
        stored_signature = license_data.get("signature", "")
        
        # Verify hardware ID
        current_hwid = get_hardware_id()
        if stored_hwid != current_hwid:
            return False, "Hardware ID mismatch"
        
        # Verify signature format
        if len(stored_signature) != 64:  # SHA256 length
            return False, "Invalid signature format"
        
        return True, f"Licensed to: {stored_email}"
        
    except Exception as e:
        return False, f"License verification failed: {str(e)}"

class SoftwareApp:
    def __init__(self, root):
        self.root = root
        self.root.title("My Software")
        self.root.geometry("500x450")
        self.root.resizable(False, False)
        self.root.configure(bg='#f8fafc')

        # Add window icon if available
        try:
            self.root.iconbitmap('logo.ico')
        except:
            pass

        # Center window
        self.center_window()

        # Check license status
        is_valid, message = verify_license()

        if is_valid:
            self.show_main_application(message)
        else:
            self.show_activation_screen(message)

    def create_context_menu(self, widget):
        """Create enhanced right-click context menu for text widgets"""
        context_menu = tk.Menu(self.root, tearoff=0, bg='#ffffff', fg='#1e293b',
                              activebackground='#3b82f6', activeforeground='#ffffff',
                              font=('Arial', 10, 'bold'), relief=tk.FLAT, bd=1)

        # Enhanced menu items with better icons and styling
        context_menu.add_command(label="📋 نسخ النص", command=lambda: self.copy_text(widget),
                               accelerator="Ctrl+C")
        context_menu.add_command(label="📄 لصق النص", command=lambda: self.paste_text(widget),
                               accelerator="Ctrl+V")
        context_menu.add_separator()
        context_menu.add_command(label="🔄 تحديد الكل", command=lambda: self.select_all(widget),
                               accelerator="Ctrl+A")
        context_menu.add_separator()
        context_menu.add_command(label="🗑️ مسح النص", command=lambda: self.clear_text(widget))

        def show_context_menu(event):
            try:
                # Update menu state based on widget content
                if hasattr(widget, 'get'):
                    has_text = bool(widget.get())
                    has_selection = widget.selection_present() if hasattr(widget, 'selection_present') else False
                else:
                    has_text = True
                    has_selection = True

                # Enable/disable menu items based on context
                context_menu.entryconfig(0, state='normal' if has_selection or has_text else 'disabled')  # Copy
                context_menu.entryconfig(1, state='normal' if widget['state'] != 'readonly' else 'disabled')  # Paste
                context_menu.entryconfig(3, state='normal' if has_text else 'disabled')  # Select All
                context_menu.entryconfig(5, state='normal' if has_text and widget['state'] != 'readonly' else 'disabled')  # Clear

                context_menu.tk_popup(event.x_root, event.y_root)
            except Exception as e:
                print(f"Context menu error: {e}")
            finally:
                context_menu.grab_release()

        widget.bind("<Button-3>", show_context_menu)
        return context_menu

    def copy_text(self, widget):
        """Copy selected text from widget with enhanced feedback"""
        try:
            copied_text = ""
            if hasattr(widget, 'selection_get'):
                try:
                    copied_text = widget.selection_get()
                except:
                    copied_text = widget.get() if hasattr(widget, 'get') else ""
            elif hasattr(widget, 'get'):
                # For Entry widgets
                if hasattr(widget, 'selection_present') and widget.selection_present():
                    copied_text = widget.selection_get()
                else:
                    copied_text = widget.get()

            if copied_text:
                self.root.clipboard_clear()
                self.root.clipboard_append(copied_text)
                # Show brief success message
                messagebox.showinfo("✅ تم النسخ", f"تم نسخ النص بنجاح!\n\nالنص المنسوخ: {copied_text[:50]}{'...' if len(copied_text) > 50 else ''}")
        except Exception as e:
            messagebox.showerror("❌ خطأ", f"فشل في نسخ النص: {str(e)}")

    def paste_text(self, widget):
        """Paste text to widget with enhanced feedback"""
        try:
            if hasattr(widget, 'insert') and widget['state'] != 'readonly':
                clipboard_text = self.root.clipboard_get()
                if clipboard_text:
                    # Clear selection if exists
                    if hasattr(widget, 'selection_present') and widget.selection_present():
                        widget.delete(tk.SEL_FIRST, tk.SEL_LAST)

                    # Insert at cursor position
                    insert_pos = widget.index(tk.INSERT) if hasattr(widget, 'index') else tk.END
                    widget.insert(insert_pos, clipboard_text)

                    # Show brief success message
                    messagebox.showinfo("✅ تم اللصق", f"تم لصق النص بنجاح!\n\nالنص الملصق: {clipboard_text[:50]}{'...' if len(clipboard_text) > 50 else ''}")
                else:
                    messagebox.showwarning("⚠️ تحذير", "الحافظة فارغة!")
            else:
                messagebox.showwarning("⚠️ تحذير", "لا يمكن اللصق في هذا الحقل!")
        except Exception as e:
            messagebox.showerror("❌ خطأ", f"فشل في لصق النص: {str(e)}")

    def select_all(self, widget):
        """Select all text in widget"""
        try:
            if hasattr(widget, 'select_range'):
                widget.select_range(0, tk.END)
                widget.focus_set()
            elif hasattr(widget, 'tag_add'):
                widget.tag_add(tk.SEL, "1.0", tk.END)
                widget.focus_set()
            messagebox.showinfo("✅ تم التحديد", "تم تحديد كامل النص!")
        except Exception as e:
            messagebox.showerror("❌ خطأ", f"فشل في تحديد النص: {str(e)}")

    def clear_text(self, widget):
        """Clear all text from widget"""
        try:
            if hasattr(widget, 'delete') and widget['state'] != 'readonly':
                if hasattr(widget, 'get') and widget.get():
                    result = messagebox.askyesno("🗑️ مسح النص", "هل أنت متأكد من مسح كامل النص؟")
                    if result:
                        widget.delete(0, tk.END)
                        messagebox.showinfo("✅ تم المسح", "تم مسح النص بنجاح!")
                else:
                    messagebox.showinfo("ℹ️ معلومة", "الحقل فارغ بالفعل!")
            else:
                messagebox.showwarning("⚠️ تحذير", "لا يمكن مسح النص من هذا الحقل!")
        except Exception as e:
            messagebox.showerror("❌ خطأ", f"فشل في مسح النص: {str(e)}")

    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = 500
        height = 450
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def show_activation_screen(self, error_message):
        """Show enhanced activation screen when license is invalid"""
        self.clear_window()

        # Main frame with gradient background
        main_frame = tk.Frame(self.root, bg='#f8fafc', padx=40, pady=30)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Header section
        header_frame = tk.Frame(main_frame, bg='#1e40af', relief=tk.FLAT, bd=0, pady=20)
        header_frame.pack(fill=tk.X, pady=(0, 25))

        # Title with modern design
        title_label = tk.Label(header_frame, text="🔐 تسجيل الدخول",
                              font=("Arial", 18, "bold"), bg='#1e40af', fg='#ffffff')
        title_label.pack()

        subtitle_label = tk.Label(header_frame, text="أدخل بياناتك للوصول إلى البرنامج",
                                 font=("Arial", 11), bg='#1e40af', fg='#e0e7ff')
        subtitle_label.pack(pady=(5, 0))

        # Status message
        if error_message != "License file not found":
            status_frame = tk.Frame(main_frame, bg='#fef2f2', relief=tk.SOLID, bd=1, padx=15, pady=10)
            status_frame.pack(fill=tk.X, pady=(0, 20))

            error_label = tk.Label(status_frame, text=f"⚠️ {error_message}",
                                  font=("Arial", 10), bg='#fef2f2', fg='#dc2626')
            error_label.pack()

        # Activation form with enhanced design
        form_frame = tk.Frame(main_frame, bg='#ffffff', relief=tk.SOLID, bd=1, padx=30, pady=30)
        form_frame.pack(fill=tk.X, pady=(0, 20))

        # Email field
        email_label = tk.Label(form_frame, text="📧 البريد الإلكتروني:", font=("Arial", 12, "bold"),
                              bg='#ffffff', fg='#374151')
        email_label.pack(anchor=tk.W, pady=(0, 8))

        self.email_entry = tk.Entry(form_frame, font=("Arial", 11), width=40,
                                   relief=tk.SOLID, bd=1, bg='#f9fafb', fg='#111827')
        self.email_entry.pack(pady=(0, 20), ipady=8)

        # Add context menu to email entry
        self.create_context_menu(self.email_entry)

        # Password field
        password_label = tk.Label(form_frame, text="🔑 كلمة المرور:", font=("Arial", 12, "bold"),
                                 bg='#ffffff', fg='#374151')
        password_label.pack(anchor=tk.W, pady=(0, 8))

        self.password_entry = tk.Entry(form_frame, show="*", font=("Arial", 11),
                                      width=40, relief=tk.SOLID, bd=1, bg='#f9fafb', fg='#111827')
        self.password_entry.pack(pady=(0, 25), ipady=8)

        # Add context menu to password entry
        self.create_context_menu(self.password_entry)

        # Separator line for visual clarity
        separator = tk.Frame(form_frame, bg='#d1d5db', height=2)
        separator.pack(fill=tk.X, pady=(20, 20))

        # Login button section with enhanced design
        login_section = tk.Frame(form_frame, bg='#ffffff', pady=10)
        login_section.pack(fill=tk.X)

        # Main login button - VERY VISIBLE
        login_btn = tk.Button(login_section, text="🚀 تسجيل الدخول",
                               command=self.attempt_login,
                               font=("Arial", 16, "bold"), bg='#dc2626', fg='white',
                               relief=tk.RAISED, cursor="hand2", width=20, pady=20,
                               activebackground='#b91c1c', activeforeground='white',
                               bd=3)
        login_btn.pack(pady=(10, 15))

        # Login instruction with better styling
        instruction_frame = tk.Frame(login_section, bg='#fef3c7', relief=tk.SOLID, bd=1, padx=10, pady=8)
        instruction_frame.pack(fill=tk.X, pady=(5, 10))

        login_instruction = tk.Label(instruction_frame, text="👆 اضغط على الزر الأحمر أعلاه للمتابعة إلى شاشة التفعيل",
                                    font=("Arial", 11, "bold"), bg='#fef3c7', fg='#92400e')
        login_instruction.pack()

        # Additional visual cue
        arrow_label = tk.Label(login_section, text="⬆️ ⬆️ ⬆️",
                              font=("Arial", 14), bg='#ffffff', fg='#dc2626')
        arrow_label.pack()
        
        # Hardware ID section
        hw_frame = tk.Frame(main_frame, bg='#e3f2fd', relief=tk.SOLID, bd=1, padx=20, pady=15)
        hw_frame.pack(fill=tk.X)

        tk.Label(hw_frame, text="� معرف الجهاز:", font=("Arial", 11, "bold"),
                bg='#e3f2fd', fg='#1565c0').pack()

        hwid = get_hardware_id()
        self.hwid_var = tk.StringVar(value=hwid)

        hwid_entry = tk.Entry(hw_frame, textvariable=self.hwid_var, state="readonly",
                             font=("Courier", 10), bg='#ffffff', fg='#333',
                             relief=tk.SOLID, bd=1, justify=tk.CENTER, width=25)
        hwid_entry.pack(pady=(10, 15))

        # Add context menu to hardware ID entry
        self.create_context_menu(hwid_entry)

        # Copy all data button
        copy_all_btn = tk.Button(hw_frame, text="📋 نسخ بيانات التفعيل",
                               command=self.copy_activation_data,
                               font=("Arial", 11, "bold"), bg='#4caf50', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=25, pady=8)
        copy_all_btn.pack(pady=(0, 20))

        # Contact message with enhanced styling
        contact_header = tk.Frame(hw_frame, bg='#e3f2fd', pady=10)
        contact_header.pack(pady=(0, 15))

        tk.Label(contact_header, text="📞 للتواصل مع المطور للتفعيل",
                font=("Arial", 13, "bold"), bg='#e3f2fd', fg='#1565c0').pack()

        tk.Label(contact_header, text="اختر الطريقة المناسبة لك:",
                font=("Arial", 10), bg='#e3f2fd', fg='#424242').pack(pady=(3, 0))

        # Contact buttons frame with enhanced design
        contact_buttons_frame = tk.Frame(hw_frame, bg='#e3f2fd')
        contact_buttons_frame.pack(pady=(0, 15))

        # WhatsApp button with enhanced functionality
        whatsapp_btn = tk.Button(contact_buttons_frame, text="📱 واتساب",
                               command=lambda: webbrowser.open("https://wa.me/201200578402"),
                               font=("Arial", 11, "bold"), bg='#25D366', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=13, pady=10,
                               activebackground='#128C7E', activeforeground='white')
        whatsapp_btn.pack(side=tk.LEFT, padx=8)

        # Facebook button with enhanced functionality
        facebook_btn = tk.Button(contact_buttons_frame, text="📘 فيسبوك",
                               command=lambda: webbrowser.open("https://www.facebook.com/mohamed.abdalkareem.558739?mibextid=rS40aB7S9Ucbxw6v"),
                               font=("Arial", 11, "bold"), bg='#1877f2', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=13, pady=10,
                               activebackground='#166fe5', activeforeground='white')
        facebook_btn.pack(side=tk.LEFT, padx=8)

        # Telegram button with enhanced functionality
        telegram_btn = tk.Button(contact_buttons_frame, text="✈️ تلجرام",
                               command=lambda: webbrowser.open("http://t.me/Mohamed_Abdo26"),
                               font=("Arial", 11, "bold"), bg='#0088cc', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=13, pady=10,
                               activebackground='#0077b3', activeforeground='white')
        telegram_btn.pack(side=tk.LEFT, padx=8)

        # Enhanced instructions with modern styling
        instructions_frame = tk.Frame(hw_frame, bg='#bbdefb', relief=tk.FLAT, bd=0, padx=15, pady=15)
        instructions_frame.pack(fill=tk.X, pady=(15, 0))

        instructions = """💡 خطوات التفعيل السريع:

1️⃣ أدخل البريد الإلكتروني وكلمة المرور أعلاه
2️⃣ اضغط "نسخ بيانات التفعيل" لنسخ جميع البيانات
3️⃣ اختر إحدى وسائل التواصل (واتساب، فيسبوك، تلجرام)
4️⃣ أرسل البيانات المنسوخة للمطور
5️⃣ ستحصل على ملف التفعيل خلال دقائق

⚡ التفعيل سريع ومضمون - خدمة 24/7"""

        tk.Label(instructions_frame, text=instructions, font=("Arial", 9),
                bg='#bbdefb', fg='#1565c0', justify=tk.LEFT).pack()

    def show_main_application(self, license_info):
        """Show main application when license is valid"""
        self.clear_window()
        self.root.title("My Software - Licensed")
        
        # Main frame
        main_frame = tk.Frame(self.root, bg='#f0f0f0', padx=40, pady=40)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Success header
        header_frame = tk.Frame(main_frame, bg='#e8f5e8', relief=tk.SOLID, bd=1, padx=20, pady=20)
        header_frame.pack(fill=tk.X, pady=(0, 30))
        
        tk.Label(header_frame, text="✅ Software Successfully Activated!", 
                font=("Arial", 16, "bold"), bg='#e8f5e8', fg='#2e7d32').pack()
        
        tk.Label(header_frame, text=license_info, 
                font=("Arial", 10), bg='#e8f5e8', fg='#388e3c').pack(pady=(5, 0))
        
        # Application content
        content_frame = tk.Frame(main_frame, bg='#ffffff', relief=tk.SOLID, bd=1, padx=30, pady=30)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        tk.Label(content_frame, text="🚀 Welcome to Your Licensed Software!", 
                font=("Arial", 18, "bold"), bg='#ffffff', fg='#1976d2').pack(pady=(0, 20))
        
        tk.Label(content_frame, text="All features are now available for use.\nThank you for your purchase!", 
                font=("Arial", 12), bg='#ffffff', fg='#666', justify=tk.CENTER).pack(pady=(0, 30))
        
        # Feature buttons
        features_frame = tk.Frame(content_frame, bg='#ffffff')
        features_frame.pack(pady=20)
        
        tk.Button(features_frame, text="📊 Analytics", command=self.feature_analytics,
                 font=("Arial", 10), bg='#ff9800', fg='white', relief=tk.FLAT,
                 cursor="hand2", width=12, pady=8).pack(side=tk.LEFT, padx=5)
        
        tk.Button(features_frame, text="⚙️ Settings", command=self.feature_settings,
                 font=("Arial", 10), bg='#9c27b0', fg='white', relief=tk.FLAT,
                 cursor="hand2", width=12, pady=8).pack(side=tk.LEFT, padx=5)
        
        tk.Button(features_frame, text="📈 Reports", command=self.feature_reports,
                 font=("Arial", 10), bg='#00bcd4', fg='white', relief=tk.FLAT,
                 cursor="hand2", width=12, pady=8).pack(side=tk.LEFT, padx=5)
        
        # License details
        details_frame = tk.Frame(content_frame, bg='#f5f5f5', relief=tk.SOLID, bd=1, padx=15, pady=15)
        details_frame.pack(fill=tk.X, pady=(30, 0))
        
        try:
            with open(LICENSE_FILE, "r") as f:
                encoded_data = f.read().strip()
            decoded_data = base64.b64decode(encoded_data).decode()
            license_data = json.loads(decoded_data)
            
            details_text = f"License Details:\nEmail: {license_data.get('email', 'Unknown')}\nHardware ID: {license_data.get('hwid', 'Unknown')}"
            tk.Label(details_frame, text=details_text, font=("Arial", 9), 
                    bg='#f5f5f5', fg='#666', justify=tk.LEFT).pack()
        except:
            pass
        
        # Exit button
        tk.Button(content_frame, text="❌ Exit Application", command=self.root.quit,
                 font=("Arial", 10), bg='#f44336', fg='white', relief=tk.FLAT,
                 cursor="hand2", width=20, pady=8).pack(pady=(20, 0))

    def attempt_login(self):
        """Attempt to login - if fails, show activation details"""
        email = self.email_entry.get().strip()
        password = self.password_entry.get().strip()

        if not email or not password:
            messagebox.showerror("خطأ", "يرجى إدخال البريد الإلكتروني وكلمة المرور")
            return

        if "@" not in email:
            messagebox.showerror("خطأ", "يرجى إدخال بريد إلكتروني صحيح")
            return

        # Try to verify with existing license (this will always fail for new users)
        hwid = get_hardware_id()

        # Since this is for new users, show activation details
        activation_message = f"""طلب تفعيل البرنامج

البريد الإلكتروني: {email}
كلمة المرور: {password}
معرف الجهاز: {hwid}

يرجى إرسال هذه المعلومات للحصول على ملف التفعيل."""

        # Show activation dialog with contact links
        self.show_activation_dialog("بيانات التفعيل", activation_message)

    def open_link(self, url):
        """Open URL in default browser with error handling"""
        try:
            webbrowser.open(url)
            messagebox.showinfo("🌐 تم فتح الرابط",
                              "تم فتح الرابط في المتصفح الافتراضي.\n"
                              "إذا لم يفتح تلقائياً، يمكنك نسخ الرابط يدوياً.")
        except Exception:
            messagebox.showerror("❌ خطأ في فتح الرابط",
                               f"لم يتمكن من فتح الرابط تلقائياً.\n\n"
                               f"الرابط: {url}\n\n"
                               f"يمكنك نسخ الرابط ولصقه في المتصفح يدوياً.")

    def copy_activation_data(self):
        """Copy all activation data to clipboard with enhanced message"""
        email = self.email_entry.get().strip()
        password = self.password_entry.get().strip()
        hwid = get_hardware_id()

        if not email or not password:
            messagebox.showerror("⚠️ خطأ في البيانات",
                               "يرجى إدخال البريد الإلكتروني وكلمة المرور أولاً\n\n"
                               "هذه البيانات مطلوبة لإنشاء ملف التفعيل الخاص بك.")
            return

        activation_data = f"""🔐 طلب تفعيل البرنامج

📧 البريد الإلكتروني: {email}
🔑 كلمة المرور: {password}
💻 معرف الجهاز: {hwid}

📝 ملاحظة: يرجى إرسال هذه المعلومات للمطور للحصول على ملف التفعيل.
⚡ التفعيل سريع ومضمون - خدمة 24/7"""

        self.root.clipboard_clear()
        self.root.clipboard_append(activation_data)

        # Show enhanced success message
        messagebox.showinfo("✅ تم النسخ بنجاح!",
                          "تم نسخ بيانات التفعيل بنجاح!\n\n"
                          "📋 البيانات جاهزة للإرسال\n"
                          "📞 استخدم روابط التواصل أدناه لإرسالها للمطور\n"
                          "⚡ ستحصل على ملف التفعيل خلال دقائق")

    def copy_hwid(self):
        """Copy hardware ID to clipboard"""
        hwid = self.hwid_var.get()
        self.root.clipboard_clear()
        self.root.clipboard_append(hwid)
        messagebox.showinfo("Copied", f"Hardware ID copied to clipboard:\n{hwid}")

    def show_activation_dialog(self, title, message):
        """Show professional activation dialog with enhanced design and contact links"""
        dialog = tk.Toplevel(self.root)
        dialog.title(title)
        dialog.geometry("700x650")
        dialog.resizable(False, False)
        dialog.configure(bg='#f8fafc')
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (350)
        y = (dialog.winfo_screenheight() // 2) - (325)
        dialog.geometry(f'700x650+{x}+{y}')

        # Main container with gradient-like effect
        main_frame = tk.Frame(dialog, bg='#f8fafc', padx=25, pady=25)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Header section with modern design
        header_frame = tk.Frame(main_frame, bg='#1e40af', relief=tk.FLAT, bd=0)
        header_frame.pack(fill=tk.X, pady=(0, 25))

        # Header content
        header_content = tk.Frame(header_frame, bg='#1e40af', pady=25)
        header_content.pack(fill=tk.X)

        # Title with icon and subtitle
        tk.Label(header_content, text="🔐 بيانات التفعيل",
                font=("Arial", 20, "bold"), bg='#1e40af', fg='#ffffff').pack()

        tk.Label(header_content, text="نسخ ومشاركة معلومات التفعيل مع المطور",
                font=("Arial", 12), bg='#1e40af', fg='#e0e7ff').pack(pady=(8, 0))

        # Data section with table design
        data_section = tk.Frame(main_frame, bg='#ffffff', relief=tk.SOLID, bd=1)
        data_section.pack(fill=tk.X, pady=(0, 20))

        # Data content
        data_content = tk.Frame(data_section, bg='#ffffff', padx=25, pady=20)
        data_content.pack(fill=tk.BOTH, expand=True)

        # Data label
        tk.Label(data_content, text="📋 بيانات التفعيل المطلوبة:",
                font=("Arial", 14, "bold"), bg='#ffffff', fg='#1e293b').pack(pady=(0, 15))

        # Table frame with border
        table_frame = tk.Frame(data_content, bg='#f8fafc', relief=tk.SOLID, bd=1)
        table_frame.pack(fill=tk.X, pady=(0, 20))

        # Extract data from message
        lines = message.split('\n')
        email = ""
        password = ""
        hwid = ""

        for line in lines:
            if "البريد الإلكتروني:" in line:
                email = line.split(":")[-1].strip()
            elif "كلمة المرور:" in line:
                password = line.split(":")[-1].strip()
            elif "معرف الجهاز:" in line:
                hwid = line.split(":")[-1].strip()

        # Table header
        header_frame = tk.Frame(table_frame, bg='#1e40af', height=40)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="البيان", font=("Arial", 11, "bold"),
                bg='#1e40af', fg='white', width=20).pack(side=tk.LEFT, padx=10, pady=8)
        tk.Label(header_frame, text="القيمة", font=("Arial", 11, "bold"),
                bg='#1e40af', fg='white').pack(side=tk.LEFT, padx=10, pady=8)

        # Table rows
        rows_data = [
            ("📧 البريد الإلكتروني", email),
            ("🔑 كلمة المرور", password),
            ("💻 معرف الجهاز", hwid)
        ]

        for i, (label, value) in enumerate(rows_data):
            row_bg = '#f1f5f9' if i % 2 == 0 else '#ffffff'
            row_frame = tk.Frame(table_frame, bg=row_bg, height=35)
            row_frame.pack(fill=tk.X)
            row_frame.pack_propagate(False)

            # Label column
            label_frame = tk.Frame(row_frame, bg=row_bg, width=200)
            label_frame.pack(side=tk.LEFT, fill=tk.Y)
            label_frame.pack_propagate(False)

            tk.Label(label_frame, text=label, font=("Arial", 10, "bold"),
                    bg=row_bg, fg='#374151').pack(padx=10, pady=8, anchor=tk.W)

            # Value column
            value_frame = tk.Frame(row_frame, bg=row_bg)
            value_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

            # Create read-only entry for value
            value_entry = tk.Entry(value_frame, font=("Consolas", 10), bg='white',
                                 fg='#1e293b', relief=tk.FLAT, bd=5, state='readonly',
                                 readonlybackground='white')
            value_entry.pack(fill=tk.X, padx=10, pady=5)

            # Insert value
            value_entry.config(state='normal')
            value_entry.delete(0, tk.END)
            value_entry.insert(0, value)
            value_entry.config(state='readonly')

            # Add context menu to each value entry
            self.create_context_menu(value_entry)

        # Copy button with modern design
        def copy_all_data():
            dialog.clipboard_clear()
            dialog.clipboard_append(message)
            messagebox.showinfo("✅ تم النسخ بنجاح",
                              "تم نسخ بيانات التفعيل بنجاح!\n\n"
                              "يمكنك الآن إرسالها للمطور عبر إحدى وسائل التواصل أدناه.")

        copy_btn = tk.Button(data_content, text="📋 انسخ البيانات", command=copy_all_data,
                           font=("Arial", 12, "bold"), bg='#10b981', fg='white',
                           relief=tk.FLAT, cursor="hand2", width=25, pady=12,
                           activebackground='#059669', activeforeground='white')
        copy_btn.pack(pady=(10, 0))

        # Contact section with clean design
        contact_section = tk.Frame(main_frame, bg='#f0f9ff', relief=tk.SOLID, bd=1)
        contact_section.pack(fill=tk.X, pady=(0, 20))

        # Contact content
        contact_content = tk.Frame(contact_section, bg='#f0f9ff', padx=25, pady=20)
        contact_content.pack(fill=tk.X)

        # Contact header
        tk.Label(contact_content, text="📞 للتواصل مع المطور للتفعيل",
                font=("Arial", 16, "bold"), bg='#f0f9ff', fg='#1e40af').pack(pady=(0, 15))

        # Contact buttons in organized layout
        buttons_container = tk.Frame(contact_content, bg='#f0f9ff')
        buttons_container.pack(pady=(0, 15))

        # Create a grid-like layout for buttons
        buttons_frame = tk.Frame(buttons_container, bg='#f0f9ff')
        buttons_frame.pack()

        # WhatsApp button with working functionality
        whatsapp_btn = tk.Button(buttons_frame, text="📱 واتساب",
                               command=lambda: webbrowser.open("https://wa.me/201200578402"),
                               font=("Arial", 12, "bold"), bg='#25D366', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=14, pady=15,
                               activebackground='#128C7E', activeforeground='white')
        whatsapp_btn.grid(row=0, column=0, padx=15, pady=5)

        # Facebook button with working functionality
        facebook_btn = tk.Button(buttons_frame, text="📘 فيسبوك",
                               command=lambda: webbrowser.open("https://www.facebook.com/mohamed.abdalkareem.558739?mibextid=rS40aB7S9Ucbxw6v"),
                               font=("Arial", 12, "bold"), bg='#1877f2', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=14, pady=15,
                               activebackground='#166fe5', activeforeground='white')
        facebook_btn.grid(row=0, column=1, padx=15, pady=5)

        # Telegram button with working functionality
        telegram_btn = tk.Button(buttons_frame, text="✈️ تلجرام",
                               command=lambda: webbrowser.open("http://t.me/Mohamed_Abdo26"),
                               font=("Arial", 12, "bold"), bg='#0088cc', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=14, pady=15,
                               activebackground='#0077b3', activeforeground='white')
        telegram_btn.grid(row=0, column=2, padx=15, pady=5)

        # Contact instruction
        tk.Label(contact_content, text="اضغط على أي من الأزرار أعلاه للتواصل المباشر",
                font=("Arial", 10), bg='#f0f9ff', fg='#64748b').pack()

        # Instructions with clean design
        instructions_section = tk.Frame(main_frame, bg='#ecfdf5', relief=tk.SOLID, bd=1)
        instructions_section.pack(fill=tk.X, pady=(0, 20))

        instructions_content = tk.Frame(instructions_section, bg='#ecfdf5', padx=25, pady=20)
        instructions_content.pack(fill=tk.X)

        # Instructions header
        tk.Label(instructions_content, text="💡 خطوات التفعيل:",
                font=("Arial", 14, "bold"), bg='#ecfdf5', fg='#059669').pack(pady=(0, 15))

        # Steps in organized format
        steps = [
            "1️⃣ اضغط على زر 'انسخ البيانات' أعلاه",
            "2️⃣ اختر وسيلة التواصل المناسبة",
            "3️⃣ أرسل البيانات المنسوخة للمطور",
            "4️⃣ انتظر ملف التفعيل (خلال دقائق)",
            "5️⃣ ضع الملف بجانب البرنامج وأعد التشغيل"
        ]

        for step in steps:
            step_frame = tk.Frame(instructions_content, bg='#ecfdf5')
            step_frame.pack(fill=tk.X, pady=2)

            tk.Label(step_frame, text=step, font=("Arial", 11),
                    bg='#ecfdf5', fg='#374151', anchor=tk.W).pack(anchor=tk.W)

        # Service note
        service_frame = tk.Frame(instructions_content, bg='#d1fae5', relief=tk.FLAT, bd=0, padx=15, pady=10)
        service_frame.pack(fill=tk.X, pady=(15, 0))

        tk.Label(service_frame, text="⚡ التفعيل سريع ومضمون - خدمة متاحة 24/7",
                font=("Arial", 11, "bold"), bg='#d1fae5', fg='#059669').pack()

        # Close button with modern design
        close_frame = tk.Frame(main_frame, bg='#f8fafc')
        close_frame.pack(fill=tk.X)

        tk.Button(close_frame, text="❌ إغلاق النافذة", command=dialog.destroy,
                 font=("Arial", 11, "bold"), bg='#ef4444', fg='white',
                 relief=tk.FLAT, cursor="hand2", width=20, pady=12,
                 activebackground='#dc2626', activeforeground='white').pack()

    def show_copyable_dialog(self, title, message, hwid):
        """Show professional dialog with copyable text and contact links"""
        dialog = tk.Toplevel(self.root)
        dialog.title(title)
        dialog.geometry("650x700")
        dialog.resizable(False, False)
        dialog.configure(bg='#f8f9fa')
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (325)
        y = (dialog.winfo_screenheight() // 2) - (350)
        dialog.geometry(f'650x700+{x}+{y}')

        # Main frame with gradient-like background
        main_frame = tk.Frame(dialog, bg='#f8f9fa', padx=25, pady=25)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Header section with modern design
        header_frame = tk.Frame(main_frame, bg='#2c3e50', relief=tk.FLAT, bd=0)
        header_frame.pack(fill=tk.X, pady=(0, 25))

        # Title with icon
        title_frame = tk.Frame(header_frame, bg='#2c3e50', pady=20)
        title_frame.pack(fill=tk.X)

        tk.Label(title_frame, text="� بيانات التفعيل",
                font=("Arial", 18, "bold"), bg='#2c3e50', fg='#ecf0f1').pack()

        tk.Label(title_frame, text="نسخ ومشاركة معلومات التفعيل",
                font=("Arial", 11), bg='#2c3e50', fg='#bdc3c7').pack(pady=(5, 0))

        # Data section with modern card design
        data_frame = tk.Frame(main_frame, bg='#ffffff', relief=tk.FLAT, bd=0)
        data_frame.pack(fill=tk.X, pady=(0, 20))

        # Add subtle shadow effect with multiple frames
        shadow_frame = tk.Frame(main_frame, bg='#e9ecef', height=2)
        shadow_frame.pack(fill=tk.X, pady=(0, 18))

        # Data content
        data_content = tk.Frame(data_frame, bg='#ffffff', padx=25, pady=25)
        data_content.pack(fill=tk.BOTH, expand=True)

        tk.Label(data_content, text="📋 معلومات التفعيل:",
                font=("Arial", 12, "bold"), bg='#ffffff', fg='#2c3e50').pack(anchor=tk.W, pady=(0, 15))

        # Text area with modern styling
        text_frame = tk.Frame(data_content, bg='#f8f9fa', relief=tk.FLAT, bd=1)
        text_frame.pack(fill=tk.X, pady=(0, 20))

        text_widget = tk.Text(text_frame, font=("Consolas", 10), bg='#f8f9fa', fg='#2c3e50',
                             relief=tk.FLAT, bd=15, wrap=tk.WORD, selectbackground='#3498db',
                             selectforeground='white', height=8)
        text_widget.pack(fill=tk.X)

        # Insert message and select all
        text_widget.insert(tk.END, message)
        text_widget.select_range(0, tk.END)
        text_widget.focus_set()

        # Copy buttons section
        copy_frame = tk.Frame(data_content, bg='#ffffff')
        copy_frame.pack(fill=tk.X, pady=(0, 20))

        # Copy all button with modern design
        def copy_all():
            dialog.clipboard_clear()
            dialog.clipboard_append(message)
            messagebox.showinfo("✅ تم النسخ", "تم نسخ بيانات التفعيل بنجاح!\nيمكنك الآن إرسالها للمطور.")

        copy_all_btn = tk.Button(copy_frame, text="📋 نسخ جميع البيانات", command=copy_all,
                               font=("Arial", 11, "bold"), bg='#27ae60', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=25, pady=12,
                               activebackground='#229954', activeforeground='white')
        copy_all_btn.pack(pady=(0, 10))

        # Copy Hardware ID only
        def copy_hwid_only():
            dialog.clipboard_clear()
            dialog.clipboard_append(hwid)
            messagebox.showinfo("✅ تم النسخ", f"تم نسخ معرف الجهاز:\n{hwid}")

        copy_hwid_btn = tk.Button(copy_frame, text="💻 نسخ معرف الجهاز فقط", command=copy_hwid_only,
                                font=("Arial", 10, "bold"), bg='#f39c12', fg='white',
                                relief=tk.FLAT, cursor="hand2", width=25, pady=10,
                                activebackground='#e67e22', activeforeground='white')
        copy_hwid_btn.pack()

        # Contact section with professional design
        contact_main_frame = tk.Frame(main_frame, bg='#ecf0f1', relief=tk.FLAT, bd=0)
        contact_main_frame.pack(fill=tk.X, pady=(0, 20))

        contact_frame = tk.Frame(contact_main_frame, bg='#ecf0f1', padx=25, pady=25)
        contact_frame.pack(fill=tk.X)

        # Contact header
        tk.Label(contact_frame, text="📞 للتواصل مع المطور للتفعيل",
                font=("Arial", 14, "bold"), bg='#ecf0f1', fg='#2c3e50').pack(pady=(0, 15))

        tk.Label(contact_frame, text="اختر طريقة التواصل المفضلة لديك:",
                font=("Arial", 10), bg='#ecf0f1', fg='#7f8c8d').pack(pady=(0, 20))

        # Contact buttons with modern design
        contact_buttons_frame = tk.Frame(contact_frame, bg='#ecf0f1')
        contact_buttons_frame.pack(pady=(0, 20))

        # WhatsApp button
        whatsapp_btn = tk.Button(contact_buttons_frame, text="📱 واتساب",
                               command=lambda: self.open_link("https://wa.me/201200578402"),
                               font=("Arial", 11, "bold"), bg='#25D366', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=15, pady=12,
                               activebackground='#128C7E', activeforeground='white')
        whatsapp_btn.pack(side=tk.LEFT, padx=8)

        # Facebook button
        facebook_btn = tk.Button(contact_buttons_frame, text="📘 فيسبوك",
                               command=lambda: self.open_link("https://www.facebook.com/mohamed.abdalkareem.558739?mibextid=rS40aB7S9Ucbxw6v"),
                               font=("Arial", 11, "bold"), bg='#1877f2', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=15, pady=12,
                               activebackground='#166fe5', activeforeground='white')
        facebook_btn.pack(side=tk.LEFT, padx=8)

        # Telegram button
        telegram_btn = tk.Button(contact_buttons_frame, text="✈️ تلجرام",
                               command=lambda: self.open_link("http://t.me/Mohamed_Abdo26"),
                               font=("Arial", 11, "bold"), bg='#0088cc', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=15, pady=12,
                               activebackground='#0077b3', activeforeground='white')
        telegram_btn.pack(side=tk.LEFT, padx=8)

        # Instructions with modern styling
        instructions_frame = tk.Frame(contact_frame, bg='#d5dbdb', relief=tk.FLAT, bd=0, padx=20, pady=15)
        instructions_frame.pack(fill=tk.X, pady=(10, 0))

        instructions = """💡 خطوات التفعيل:
1️⃣ اضغط "نسخ جميع البيانات" أعلاه
2️⃣ اختر طريقة التواصل (واتساب، فيسبوك، أو تلجرام)
3️⃣ أرسل البيانات المنسوخة للمطور
4️⃣ ستحصل على ملف التفعيل خلال دقائق
5️⃣ ضع ملف التفعيل بجانب البرنامج وأعد التشغيل"""

        tk.Label(instructions_frame, text=instructions, font=("Arial", 9),
                bg='#d5dbdb', fg='#2c3e50', justify=tk.LEFT).pack()

        # Close button with modern design
        close_frame = tk.Frame(main_frame, bg='#f8f9fa')
        close_frame.pack(fill=tk.X)

        tk.Button(close_frame, text="❌ إغلاق النافذة", command=dialog.destroy,
                 font=("Arial", 11, "bold"), bg='#e74c3c', fg='white',
                 relief=tk.FLAT, cursor="hand2", width=20, pady=12,
                 activebackground='#c0392b', activeforeground='white').pack()

    def feature_analytics(self):
        """Sample analytics feature"""
        messagebox.showinfo("Analytics", "📊 Analytics module loaded!\nView your data insights here.")

    def feature_settings(self):
        """Sample settings feature"""
        messagebox.showinfo("Settings", "⚙️ Settings panel opened!\nConfigure your preferences here.")

    def feature_reports(self):
        """Sample reports feature"""
        messagebox.showinfo("Reports", "📈 Reports module loaded!\nGenerate and export reports here.")

    def clear_window(self):
        """Clear all widgets from window"""
        for widget in self.root.winfo_children():
            widget.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    app = SoftwareApp(root)
    root.mainloop()
